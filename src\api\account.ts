import { request } from '@/utils/request.ts'

export default {
  /**
	 * 查询子用户列表
	 */
	getChildrenUserList(tenant: string) {
	  return request({ url: `/user/v1.0/${tenant}/children`, 	method: 'get'	 });
	},

  /**
	 * 创建子用户
	 */
	createChildrenUser(tenant: string, data: {
    email: string,
    nickName: string,
    phone: string,
    pwd: string,
    state: boolean,
    tenantCode: string,
    tenantId: string,
    unit: string,
    userName: string
  }) {
	  return request({ url: `/user/v1.0/${tenant}/children/init`, method: 'post', data });
	},

  /**
	 * 更新子用户
	 */
	updateChildrenUser(tenant: string, data: {
    email: string,
    nickName: string,
    phone: string,
    pwd: string,
    state: boolean,
    tenantCode: string,
    tenantId: string,
    unit: string,
    userName: string
  }) {
	  return request({ url: `/user/v1.0/${tenant}/children/init`, method: 'put', data });
	},

  /**
	 * 更新用户状态
	 */
	updateState(tenant: string, id: string, state: number) {
	  return request({ url: `/user/v1.0/${tenant}/init/${id}/${state}`, method: 'put' });
	},

  /**
	 * 重置密码
	 */
	resetUserPwd(data: {
    userId: string
    password: string,
  }) {
	  return request({ url: `/user/v1.0/reset-pwd`, method: 'put', data });
	},

  /**
	 * 重置电话/邮箱
	 */
	resetUserModify(tenant: string, data: {
    type: number,
    userId: string
    code: string,
    phone?: string,
    email?: string
  }) {
	  return request({ url: `/user/v1.0/${tenant}/modify-user-info`, method: 'post', data });
	},
  
  /**
	 * 发送手机验证码
	 */
	sendPhoneCode(tenant: string, data: {
    type: number,
    userId: string
    phone: string,
  }) {
	  return request({ url: `/user/v1.0/${tenant}/phone-verification-code`, method: 'post', data });
	},

  /**
	 * 发送邮箱验证码
	 */
	sendEmailCode(tenant: string, data: {
    type: number,
    userId: string
    email: string
  }) {
	  return request({ url: `/user/v1.0/${tenant}/email-verification-code`, method: 'post', data });
	},
}