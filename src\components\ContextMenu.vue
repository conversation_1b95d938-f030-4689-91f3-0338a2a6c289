<template>
  <div class="contextmenu-filemana" :style="{top: `${menuPosition.y}px`, left: `${menuPosition.x}px`}">
    <div class="contextmenu-item" v-if="menus.includes('download')" @click="change('download')">
      <DownloadOutlined /> <span>下载文件</span>
    </div>
    <div class="contextmenu-item" v-if="menus.includes('refresh')" @click="change('refresh')">
      <SyncOutlined /> <span>刷新</span>
    </div>
    <div class="contextmenu-item del" v-if="menus.includes('delete')" @click="change('delete')">
      <DeleteOutlined /> <span>删除</span>
    </div>
    <div class="contextmenu-item" v-if="menus.includes('newfile')" @click="change('newfile')">
      <FolderAddOutlined /> <span>新建文件夹</span>
    </div>
    <div class="contextmenu-item" v-if="menus.includes('rename')" @click="change('rename')">
      <EditOutlined /> <span>重命名</span>
    </div>
    <div class="contextmenu-item" v-if="menus.includes('copyPath')" @click="change('copyPath')">
      <LinkOutlined /> <span>复制文件路径</span>
    </div>
    <div class="contextmenu-item" v-if="menus.includes('copyFile')" @click="change('copyFile')">
      <CopyOutlined /> <span>复制文件</span>
    </div>
    <div class="contextmenu-item" v-if="menus.includes('zipFile')" @click="change('zipFile')">
      <FileZipOutlined /> <span>压缩所选文件</span>
    </div>
    <div class="contextmenu-item" :class="transferFile.length ? '' : 'disable-item'" v-if="menus.includes('pasteFile')"@click="change('pasteFile')">
      <ReconciliationOutlined /> <span>粘贴文件</span>
    </div>
    <div class="contextmenu-item" :class="transferFile.length ? '' : 'disable-item'" v-if="menus.includes('moveFile')"@click="change('moveFile')">
      <DeliveredProcedureOutlined /> <span>移动文件</span>
    </div>
  </div>
</template>
<script setup lang="ts">
import { 
  DownloadOutlined, 
  SyncOutlined, 
  DeleteOutlined, 
  FolderAddOutlined, 
  EditOutlined, 
  CopyOutlined, 
  LinkOutlined, 
  ReconciliationOutlined, 
  DeliveredProcedureOutlined,
  FileZipOutlined
} from '@ant-design/icons-vue';
const props = defineProps<{menuPosition:  {x: number, y: number}, menus: string[], data?: any, transferFile: any[]}>();
const emit = defineEmits(['menuChange']);

/**修改 */
function change(type: string) {
  if ((type == 'pasteFile' || type == 'moveFile') && !props.transferFile.length) return;
  emit('menuChange', { menuType: type })
}

</script>
<style scoped lang="scss">
  .contextmenu-filemana {
    position: fixed;
    box-shadow: 0 6px 12px #0000002d;
    border: 1px solid rgba(0,0,0,.15);
    border-radius: 3px;
    color: #444040;
    font-size: 12px;
    z-index: 999;
    background-color: #fff;
    .contextmenu-item {
      display: block;
      line-height: 34px;
      padding: 0 10px;
      text-align: start;
      position: relative;
      color: #444040;
      span {
        margin-left: 8px;
      }
      &:hover {
        cursor: pointer;
        color: #66b1ff;
      }
      &.disable-item {
        cursor: default;
        color: #afafaf;
      }
      &.disable-item:hover {
        color: #afafaf;
      }
    }
    .del {
      &:hover {
        // background: #ff675c;
        color: #ff675c;
      }
    }
  }
</style>