<template>
  <a-modal
    style="top: 6%;"
    :zIndex="levelList.indexOf('sheet')"
    :maskClosable="false"
    :wrap-style="{ pointerEvents: 'none', overflow: 'hidden' }" 
    :mask="false"
    :wrapClassName="isFullModal ? 'full-modal' : ''" 
    :width="isFullModal ? '100%' : '75%'"
    :bodyStyle="{ height: isFullModal ? '100vh' : '75vh' }"
    @cancel="close" 
    :open="showModal" 
    :footer="null">
    <div class="container" ref="fileboxRef" @contextmenu.prevent="showMenu($event)" @click="isShowMenu = false">
      <div class="card-head">
        <a-form
          size="small"
          layout="inline"
          :model="searchData"
          name="basic"
          :label-col="{ span: 6 }"
          :wrapper-col="{ span: 17}"
          autocomplete="off"
        >
          <a-form-item>
            <a-button type="primary" @click="openSheet = true">新增工单</a-button>
          </a-form-item>
          <a-form-item label="工单名称">
            <a-input v-model:value="searchData.title" @change="getList" placeholder="请输入名称" />
          </a-form-item>
          <a-form-item label="提交时间">
            <a-range-picker v-model:value="dateArr" valueFormat="YYYY-MM-DD" @change="dateChange" />
          </a-form-item>
          <a-form-item>
            <a-button size="small" type="primary" @click="getList">
              <ReloadOutlined />刷新
            </a-button>
          </a-form-item>
        </a-form>
      </div>
      <div class="card">
        <a-table
          size="small"
          rowKey="text"
          :loading="loading"
          :scroll="{ y: tableScrolly }"
          :columns="columns.map(column => ({...column, align: 'left'}))"
          :data-source="dataList"
          :bordered="true"
          :pagination="{
            current: searchData.pageNo,
            pageSize: searchData.pageSize,
            total: total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total: any) => `共 ${total} 条`,
            onChange: handlePageChange,
            onShowSizeChange: handleSizeChange,
          }"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'imageList'">
              <img v-if="record.imageList" v-for="item in record.imageList" width="60px" height="60px" :src="item" alt="">
            </template>
            <template v-if="column.key === 'state'">
              <span>{{ sheetStatus[record.state] }}</span>
            </template>
            <template v-if="column.key === 'operate'">
              <a-button size="small" type="link" @click="openInfo(record)">进度</a-button>
              <a-button v-if="[7].includes(record.state)" size="small" type="link" @click="finishSheet(record)">结单</a-button>
              <a-button v-if="[6, 7].includes(record.state)" size="small" type="text" danger @click="backSheet(record)">退回</a-button>
              <a-button v-if="[0, 1, 2, 3, 6, 7].includes(record.state)" size="small" danger type="link" @click="closeSheet(record)">关闭</a-button>
            </template>
          </template>
        </a-table>
      </div>
    </div>
    <template #title>
      <div class="modal-head" ref="headModalRef">
        <div class="title-text">提交工单</div>
        <LineOutlined class="small-icon" @click="hide" />
        <img class="big-icon" src="@/assets/image/maxwindow.png" @click="isFullModal = !isFullModal" alt="">
      </div>
    </template>
    <template #modalRender="{ originVNode }">
      <div :style="!isFullModal ? transformStyle : ''" @click="changeLevelIndex('sheet')">
        <component :is="originVNode" />
      </div>
    </template>
  </a-modal>

  <!-- 新增/修改 -->
  <a-modal v-model:open="openSheet" title="提交工单" @cancel="restForm" :width="550">
    <a-form layout="horizontal" ref="formRef" :model="formData">
      <a-form-item label="工单标题" name="title" :rules="[{ required: true, message: '请输入工单名称' }]">
        <a-input v-model:value="formData.title" placeholder="请输入工单名称" />
      </a-form-item>
      <a-form-item label="工单描述" name="message" :rules="[{ required: true, message: '请输入工单描述' }]">
        <a-textarea type="textarea" :rows="4" v-model:value="formData.message" placeholder="请输入工单描述" />
      </a-form-item>
      <a-form-item :label-col="{ span: 4 }" label="工单截图" name="images" :rules="[{ required: false, message: '请上传工单截图' }]">
        <a-upload v-model:file-list="formData.images" list-type="picture-card" :showUploadList="{showPreviewIcon: false}" :beforeUpload="uploadRequest">
          <div v-if="formData.images.length < 5">
            <plus-outlined />
            <div style="margin-top: 8px">上传图片</div>
          </div>
        </a-upload>
      </a-form-item>
    </a-form>
    <template #footer>
      <a-button @click="openSheet = false; restForm()">取消</a-button>
      <a-button type="primary" :loading="sheetLoading" @click="submitSheet">确定</a-button>
    </template>
  </a-modal>

  <!-- 工单进度 -->
  <a-modal v-model:open="openSheetProgres" title="工单进度" :footer="null" width="50%">
    <a-steps v-model:current="currentProgres" direction="vertical" :items="progresList"></a-steps>
  </a-modal>
  
  <ContextMenu v-if="isShowMenu" :menuPosition="menuPosition" :menus="menus" @menuChange="menuChange"></ContextMenu>
</template>
<script setup lang="ts">

import { ref, onMounted, nextTick } from 'vue'
import { LineOutlined, PlusOutlined, ReloadOutlined } from '@ant-design/icons-vue';
import { useStore } from '@/store'
import { useResizeObserver  } from '@vueuse/core'
import { useDrag } from '../views/hooks/useDrag'
import ContextMenu from './ContextMenu.vue'
import workService from '../api/work'
import { message, Modal } from 'ant-design-vue';
defineProps<{ levelList: string[], changeLevelIndex:Function }>();
const emit = defineEmits(['close'])
const store = useStore();
const showModal = ref(true);
// 全屏
const isFullModal = ref(false);
// 拖拽终端
const headModalRef = ref();
const transformStyle = ref(useDrag(headModalRef));
/**表格 */
const columns = [
  { title: '工单编号', dataIndex: 'wsNo',  key: 'wsNo'},
  { title: '姓名', dataIndex: 'nickName', key: 'nickName' },
  { title: '标题', dataIndex: 'title', key: 'title' },
  { title: '图片', dataIndex: 'imageList', key: 'imageList'},
  { title: '描述', dataIndex: 'message', key: 'message'},
  { title: '状态', dataIndex: 'state', key: 'state'},
  { title: '创建时间', dataIndex: 'createTime', key: 'createTime'},
  { title: '操作', dataIndex: 'operate', key: 'operate'},
];
/**搜索 */
const dateArr = ref([]);
const searchData = ref<any>({
  title: '',
  state: '',
  pageNo: 1,
  pageSize: 20,
  startCreateTime: '',
  endCreateTime: ''
});
const fileboxRef = ref();
const tableScrolly = ref(400);
onMounted(async () => {
  store.working.push('sheet');
  await getList();
  nextTick(() => {
    useResizeObserver(fileboxRef.value, (entries) => { //监听终端窗口
      if(!fileboxRef.value) return;
      const { height } = entries[0].contentRect;
      tableScrolly.value = height - 160;
    });
  })
});

const loading = ref(false);
const dataList = ref<any>([]);
const total = ref(0);
/**获取文件管理 */
async function getList() {
  loading.value = true;
  const { data } = await workService.getSheetList(store.authInfo.tenantCode, searchData.value as any);
  dataList.value = data.list;
  total.value = data.total;
  loading.value = false;
}

/**日期筛选 */
function dateChange() {
  searchData.value.startCreateTime = dateArr.value[0];
  searchData.value.endCreateTime = dateArr.value[1];
  getList();
}

/**右键菜单 */
const isShowMenu = ref(false);
const menuPosition = ref({x: 0, y: 0});
const menus = ref(['refresh']);
function showMenu(event: any) {
  menuPosition.value.x = event.clientX;
  menuPosition.value.y = event.clientY;
  isShowMenu.value = true;
  event.stopPropagation();
}

/**右键菜单 */
function menuChange({ menuType }: any) {
  switch(menuType) {
    case 'refresh' : getList(); break;
  };
  isShowMenu.value = false;
}

/**页改变 */
function handlePageChange(page: number) {
  searchData.value.pageNo = page;
  getList();
}

/**页数改变 */
function handleSizeChange(current: number, size: number) {
  searchData.value.pageNo = current;
  searchData.value.pageSize = size;
  getList();
}

const openSheet = ref(false);
const sheetStatus = ref<{[key: string] : string}>({
  0: "已提交",
  1: "已受理",
  2: "处理中",
  3: "未解决退回",
  4: "已关闭",
  5: "已结单",
  6: "系统关闭",
  7: "处理完成"
})

/**工单信息 */
const formData = ref<any>({
  title: '',
  message: '',
  images: [],
});

const formRef = ref();
const sheetLoading = ref(false);
/**提交工单 */
async function submitSheet() {
  try {
    sheetLoading.value = true;
    await formRef.value.validate();
    const data = JSON.parse(JSON.stringify(formData.value))
    data.images = data.images.map((item: any) => item.Base64)
    await workService.createdSheetInit(store.authInfo.tenantCode, data);
    getList();
    restForm();
    sheetLoading.value = false;
    openSheet.value = false;
    message.success('提交成功');
  } catch (error) {
    console.log(error);
    sheetLoading.value = false;
  }
}

/**上传截图 */
async function uploadRequest(file: File) {
  const isJPGOrPNG = file.type === "image/jpeg" || file.type === "image/png" || file.type === "image/jpg";
  const isSize = file.size / 1024 < 4096;
  if (!isJPGOrPNG) {
    message.error("上传图片只能是 JPG/PNG 格式!");
    setTimeout(() => formData.value.images.pop(), 0)
  }
  if (!isSize) {
    message.error("上传图片大小不能超过 4MB!");
    setTimeout(() => formData.value.images.pop(), 0)
  }
  // 读取文件
  const reader = new FileReader();
  reader.onload = (e: any) => {
    // console.log(e.target.result)
    // 将图片转换为Base64
    formData.value.images[formData.value.images.length - 1].Base64 = e.target.result;
  };
  // 读取为DataURL
  reader.readAsDataURL(file);
  return false
}

/**重置表单 */
function restForm() {
  formData.value = {
    title: '',
    message: '',
    images: [],
  }
}

const openSheetProgres = ref(false);
const currentProgres = ref(0);
const progresList = ref([]);
/**查看工单进度 */
function openInfo(item: any) {
  openSheetProgres.value = true;
  progresList.value = item.progresList.map((item: any) => {
    return {
      title: sheetStatus.value[item.state] + ' ' + item.createTime,
      description: item.message,
    }
  });
  currentProgres.value = item.progresList.length;
}

/**退回工单 */
async function backSheet(item: any) {
  await workService.backSheet(store.authInfo.tenantCode, item.id)
  getList();
  message.success('操作成功')
}

/**已解决工单 */
async function finishSheet(item: any) {
  await workService.finishSheet(store.authInfo.tenantCode, item.id)
  getList();
  message.success('操作成功')
}

/**关闭工单 */
async function closeSheet(item: any) {
  Modal.confirm({
    title: '提示',
    content: `确定要取消${item.title}吗`,
    onOk: async () => {
      return workService.closeSheet(store.authInfo.tenantCode, item.id).then(() => {
        getList();
        message.success('操作成功')
      })
    }
  })
}


/**打开弹窗*/
async function open() {
  showModal.value = true;
}

/**隐藏弹窗 */
function hide() {
  showModal.value = false;
}

/**关闭弹窗 */
function close() {
  showModal.value = false;
  store.working.splice(store.working.indexOf('sheet'), 1);
  emit('close')
};
defineExpose({open, close})
</script>

<style scoped lang="scss" >
@import url(./antdmodal.scss);
.container {
  width: 100%;
  margin-top: 12px;
  height: 100%;
  .card-head {
    padding: 0 20px;
  }
  :deep(.ant-page-header) {
    padding: 8px 24px;
  }
  .card {
    padding: 20px;
    :deep(.ant-table-tbody>tr>td) {
      padding-top: 4px;
      padding-bottom: 4px;
    }
    .file-name {
      cursor: pointer;
      margin-left: 10px;
    }
  }
}
</style>
