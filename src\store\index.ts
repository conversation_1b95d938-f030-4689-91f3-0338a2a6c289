import router from '@/router';
import { createPinia, defineStore } from 'pinia'
import loginService from '@/api/login'
const pinia = createPinia();
const auth_info = localStorage.getItem('auth_info');
const useStore = defineStore('store', {
  state: (): {
    authInfo: any,
    /**用户信息 */
    userInfo: any,
    // 刷新用户时间戳(用户核时欠费)
    refreshUserTime: number,
    /**弹窗运行中 */
    working: Array<string>,
    /**登录背景 */
    baseInfo: {
      portalTitle: string
      portalBackgroundImage: string,
      consoleBackgroundImage: string,
    },
  } => { 
    return { 
      authInfo: auth_info ? JSON.parse(auth_info) : {},
      userInfo: null,
      refreshUserTime: 0,
      working: [],
      baseInfo: { // 默认
        portalTitle: '天玑智算云',
        portalBackgroundImage: 'login.jpg',
        consoleBackgroundImage: 'desktop.jpg'
      },
    }
  },

  getters: {},

  actions: {
    /**背景信息 */
    setBaseInfo(result: any) {
      this.$patch(state => {  
        if(result)state.baseInfo = result;
      });
    },

    /**存储登录信息 */
    setAuthInfo(result: any) {
      result.expireTime = new Date(new Date().getTime() + result.effectiveTime * 1000);
      localStorage.setItem('auth_info', JSON.stringify(result));
      this.$patch(state => {  
        state.authInfo = result;
      });
    },

    /*** 刷新token */
    async refreshToken() {
      const { data } = await loginService.refreshToken();
      this.authInfo.token = data.token;
      this.setAuthInfo(this.authInfo)
      return data.token;
    },

    /**
     * 存储用户信息
     * @param result 
     */
    setUserInfo(result: any) {
      this.$patch(state => {  
        state.userInfo = result;
      });
    },

    outLogin() {
      localStorage.removeItem('auth_info');
      localStorage.clear();
      this.$patch(state => {
        // if (state.authInfo.token) loginService.logout();
        state.authInfo = {};
        state.working = [];
      });
      router.push('/login')
    }
  },
})
export {
  useStore
}
export default pinia