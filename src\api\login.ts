import { request } from '@/utils/request.ts'
import {useStore} from '@/store'
export default {
	/**
	 * 用户登录
	 * @param {object} params
	 * @returns
	 */
	async login(params: {
    userName: string,
    passWord:  string
  }) {
	  const result = await request({ url: '/user/v1.0/login', 	method: 'post',	data: params });
		useStore().setAuthInfo(result.data);
		return result
	},

	/**
	 * 获取用户信息
	 * @returns
	 */
	async getUserInfo(tenant: string) {
	  const result = await request({ url: `/user/v1.0/${tenant}/user/info`, method: 'get' });
		useStore().setUserInfo(result.data);
		return result
	},

	/**
	 * 获取token
	 */
	refreshToken() {
	  return request({ url: `/user/v1.0/refresh-token`, 	method: 'get' });
	},

	/**
	 * 获取租户信息
	 */
	async getBaseInfo(domain: string) {
	  const result = await request({ url: `/tenant/v1.0/base`, 	method: 'post', data: {domain} });
		useStore().setBaseInfo(result.data);
		return result
	},

	/**
	 * 修改密码
	 */
	uploadPwd(data: {
		userName: string,
		oldPassword: string,
		passWord: string
	}) {
	  return request({ url: `/user/v1.0/pwd`,	method: 'put', data });
	},

	/**
	 * 退出登录
	 */
	logout() {
	  return request({ url: `/user/v1.0/logout`,	method: 'get' });
	},

	/**
	 * 登录日志
	 * @param {object} params
	 */
	getLoginLogs(tenant: string, params: {
    pageNo: number,
    pageSize: number,
		endLoginTime: string;
		startLoginTime: string;
		tenantCode: string;
		tenantName: string;
		userId: string;
		userName: string;
  }) {
	  return request({ url: `/log/v1.0/${tenant}/user/login`, 	method: 'get', params: params	 });
	},

	/**
	 * 获取公众号二维码
	 */
	getQrcode(userId: string) {
		return request({ url: `/api/wechat/qrcode/temp`,	method: 'get', params: { userId } });
	},

	/**
	 * 查询二维码状态
	 */
	getQrcodeStatus(sceneId: string) {
		return request({ url: `/api/wechat/qrcode/status`,	method: 'get', params: { sceneId } });
	},
}
