<template>
  <a-modal
    style="top: 6%;"
    :zIndex="levelList.indexOf('file')"
    :maskClosable="false"
    :wrap-style="{ pointerEvents: 'none', overflow: 'hidden' }" 
    :mask="false"
    :wrapClassName="isFullModal ? 'full-modal' : ''" 
    :width="isFullModal ? '100%' : '75%'"
    :bodyStyle="{ height: isFullModal ? '100vh' : '75vh' }"
    @cancel="close" 
    :open="showModal" 
    :footer="null">
    <div class="container" ref="fileboxRef" @contextmenu.prevent="showMenu($event)" @dragenter="isUpdataMask = true" @click="isShowMenu = false">
      <div class="card-head">
        <a-row>
          <a-col :span="4">
            <a-select size="small" v-model:value="gidNumber" style="width: 90%" @change="changeCluster"  placeholder="选择集群分区">
              <a-select-option v-for="item in store.userInfo.userClusterInfo" :value="item.gidNumber">
                {{  item.clusterName + "-" + item.groupNameDesc }}
              </a-select-option>
            </a-select>
          </a-col>
          <a-col :span="14">
            <a-space>
              <a-button size="small" @click="openUpload()">上传文件</a-button>
              <a-button size="small" @click="openUploadFolder()">上传目录</a-button>
              <a-button size="small" @click="showfolderModel = true">新建文件夹</a-button>
              <a-button size="small" type="primary" @click="getFileList(directory)">
                <ReloadOutlined />刷新
              </a-button>
            </a-space>
          </a-col>
          <a-col :span="6">
            <a-input-search
              size="small"
              v-model:value="searchKw"
              placeholder="请输入关键字"
              enter-button
              @search="getFileList(directory)"
            />
          </a-col>
        </a-row>
      </div>
      <div class="nav-list">
        <div class="nav-left">
          <div>
            <ArrowLeftOutlined @click="backDir"/>
            <span class="nav-h">&nbsp;：<HomeFilled @click="getFileList(clusterInfo.currentDirectory)" style="color: #999999;" /></span>
          </div>
          <a-breadcrumb>
            <a-breadcrumb-item v-if="navList.length" v-for="(item, i) in navList">
              <span class="nav-name" @click="navChange(i)">{{ item }}</span>
            </a-breadcrumb-item>
          </a-breadcrumb>
        </div>
        <div class="nav-right">
          <a-button type="link" size="small" @click="containHiddenFile = !containHiddenFile; getFileList(directory)">
            <EyeOutlined v-if="containHiddenFile"/>
            <EyeInvisibleOutlined v-else/>
            {{ containHiddenFile ? '不显示隐藏文件' : '显示隐藏文件' }}
          </a-button>
          <a-button type="link" size="small" @click="uploadRef.panelShow = true"><UploadOutlined />上传记录</a-button>
        </div>
      </div>
      <div class="card">
        <a-table
          size="small"
          rowKey="text"
          :loading="loading"
          :scroll="{ x: 800, y: tableScrolly }"
          :pagination="false"
          :row-selection="{ selectedRowKeys: selectedData, onChange: onSelectChange }"
          :columns="columns"
          :data-source="dataList"
        >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'text'">
            <div @dblclick="openFile(record)" style="cursor: pointer;" @contextmenu.prevent="showMenu($event, record)">
              <FolderOpenFilled v-if="record.fileType == '目录'" style="color: #f6b059;"/>
              <LinkOutlined v-else-if="record.fileType == '链接文件'" style="color: #74bfff;" />
              <FileZipOutlined v-else-if="getFileExtension(record.text) == '压缩文件'" style="color: #74bfff;" />
              <FileTextFilled v-else style="color: #74bfff;" />
              <span class="file-name">{{ record.text }}</span>
            </div>
          </template>
          <template v-else-if="column.key === 'type'">
            <span>{{ record.fileType }}</span>
          </template>
          <template v-else-if="column.key === 'updateTime'">
            <span>{{ record.updateTime }}</span>
          </template>
          <template v-else-if="column.key === 'size'">
            <span>{{ record.size }}</span>
          </template>
        </template>
        </a-table>
      </div>
      <!-- 底部按钮 -->
      <div class="card-bottom">
        <a-row justify="space-between" style="margin-top: 10px;" v-if="selectedData.length">
          <a-col>
            <a-space>
              <a-button size="small" type="primary" @click="downloadFile()" :loading="dowloading" :disabled="!selectedData.length">下载文件</a-button>
              <a-button size="small" type="primary" @click="showZipModel = true" :disabled="!selectedData.length">压缩文件</a-button>
              <a-button size="small" type="primary" danger @click="deleteFile()" :loading="delloading" :disabled="!selectedData.length">删除文件</a-button>
            </a-space>
          </a-col>
          <a-col><span>已勾选{{ selectedData.length }}个项目</span> </a-col>
        </a-row>
        <a-row style="margin-top: 10px;" v-else>
          <a-col>
            <span>{{ dataList.length }}个项目</span>
            <span style="margin-left: 16px;">个人主目录：{{ clusterInfo.currentDirectory }}</span>
          </a-col>
        </a-row>
      </div>
      <!-- 拖拽蒙版 -->
      <div class="mask-updata" v-show="isUpdataMask">
        <a-upload-dragger name="file" 
          @dragleave="isUpdataMask = false" 
          @dragover.prevent 
          @drop="isUpdataMask = false"
          :showUploadList="false" 
          :multiple="true" 
          :directory="true"
          :customRequest="(event: any) => openUpload(event.file)"
        >
          <div class="mask-tips">
            <VerticalAlignTopOutlined class="up-icon" /><span>拖拽到此处上传</span>
          </div>
        </a-upload-dragger>
      </div>
    </div>
    <template #title>
      <div class="modal-head" ref="filemodalRef">
        <div class="title-text">文件管理</div>
        <LineOutlined class="small-icon" @click="hide" />
        <img class="big-icon" src="@/assets/image/maxwindow.png" @click="isFullModal = !isFullModal" alt="">
      </div>
    </template>
    <template #modalRender="{ originVNode }">
      <div :style="!isFullModal ? transformStyle : ''" @click="changeLevelIndex('file')">
        <component :is="originVNode" />
      </div>
    </template>
  </a-modal>

  <!-- 右键菜单 -->
  <ContextMenu v-if="isShowMenu" :menuPosition="menuPosition" :menus="menus" :transferFile="transferFile" @menuChange="menuChange"></ContextMenu>
  
  <!-- 创建文件夹 -->
  <a-modal title="" v-model:open="showfolderModel" :footer="null">
    <a-form layout="horizontal" @finish="submitFolder" :model="formFolder" :label-col="{ span: 5 }" :wrapper-col="{ span: 18 }">
      <h3 style="text-align: center;">新建文件夹</h3>
      <br>
      <a-form-item 
        label="文件夹名"
        name="folderName"
        :rules="[{ required: true, message: '请输入文件夹名' }]"
      >
        <a-input style="width: 70%;" v-model:value="formFolder.folderName" placeholder="请输入文件夹名" />
        <a-button style="margin-left: 16px;" :loading="createdloading" type="primary" html-type="submit">创建</a-button>
      </a-form-item>
    </a-form>
  </a-modal>

  <!-- 文件夹重命名 -->
  <a-modal title="" v-model:open="showRenameModel" :footer="null">
    <a-form layout="horizontal" @finish="submitRename" :model="formRename" :label-col="{ span: 5 }" :wrapper-col="{ span: 18 }">
      <h3 style="text-align: center;">重命名</h3>
      <br>
      <a-form-item 
        label="文件名"
        name="newName"
        :rules="[{ required: true, message: '请输入文件名' }]"
      >
        <a-input style="width: 70%;" v-model:value="formRename.newName" placeholder="请输入文件名" />
        <a-button style="margin-left: 16px;" :loading="uploading" type="primary" html-type="submit">修改</a-button>
      </a-form-item>
    </a-form>
  </a-modal>

  <!-- 文件压缩 -->
  <a-modal title="" v-model:open="showZipModel" :maskClosable="false" :footer="null" @cancel="closeZipModel(progressTask[currentTaskId])">
    <a-form layout="horizontal" @finish="compressFile" :model="formCompress" :label-col="{ span: 5 }" :wrapper-col="{ span: 18 }">
      <h3 style="text-align: center;">压缩所选文件</h3>
      <a-list size="small" bordered :data-source="selectedList" style="padding: 0 20px; border: none;">
        <template #renderItem="{ item }">
          <a-list-item>
            <div>
              <FolderOpenFilled v-if="item.fileType == '目录'" style="color: #f6b059;"/>
              <LinkOutlined v-else-if="item.fileType == '链接文件'" style="color: #74bfff;" />
              <FileZipOutlined v-else-if="getFileExtension(item.text) == '压缩文件'" style="color: #74bfff;" />
              <FileTextFilled v-else style="color: #74bfff;" />
              <span style="margin-left: 10px;">{{ item.text }}</span>
            </div>
          </a-list-item>
        </template>
      </a-list>
      <a-progress v-if="currentTaskId && compressing" style="padding-left: 40px;" :percent="progressTask[currentTaskId].progress" status="active" />
      <a-form-item label="压缩名"  name="compressName" :rules="[{ required: true, message: '请输入压缩文件名' }]">
        <a-flex justify="space-between">
          <a-input style="width: 65%;" v-model:value="formCompress.compressName" placeholder="请输入压缩文件名">
            <template #addonAfter>
              <a-select v-model:value="format" style="width: 80px">
                <!-- <a-select-option value=".zip">.zip</a-select-option> -->
                <a-select-option value=".tar.gz">.tar.gz</a-select-option>
              </a-select>
            </template>
          </a-input>
          <a-button :loading="compressing" type="primary" html-type="submit">
            {{compressing ? "正在压缩" : "开始压缩"}}
          </a-button>
        </a-flex>
      </a-form-item>
    </a-form>
  </a-modal>

  <!-- 文件复制、移动弹窗 -->
  <a-modal v-model:open="showTransferModel.open" title="" width="500px">
    <div class="transfer">
      <h3 style="text-align: center;">{{ `远程传输 / ${showTransferModel.title}` }}</h3>
      <div class="line" style="display: flex;">
        <span>源本路径：</span>
        <div>
          <p v-for="item in transferFile">{{ item.pwd }}</p>
        </div>
      </div>
      <div class="line"><span>目标路径：</span> <span>{{ directory }}</span></div>
      <div class="transfer-tips"><InfoCircleOutlined />&nbsp;注意：远程传输可能消耗大量时间</div>
    </div>

    <template #footer>
      <a-button @click="showTransferModel.open = false;">取消</a-button>
      <a-button type="primary" :loading="transferLoading" @click="submitTransfer">确定</a-button>
    </template>
  </a-modal>

  <GlobalUploader ref="uploadRef" :params="{ currentPath: directory }" :clusterInfo="clusterInfo" :dataList="dataList"  />
</template>
<script setup lang="ts">

import { ref, onMounted, nextTick, h, computed, watch } from 'vue'
import { 
  FolderOpenFilled, 
  ArrowLeftOutlined, 
  LineOutlined, 
  FileTextFilled, 
  LinkOutlined, 
  FileZipOutlined, 
  HomeFilled, 
  VerticalAlignTopOutlined, 
  UploadOutlined, 
  EyeOutlined, 
  EyeInvisibleOutlined, 
  InfoCircleOutlined,
  ReloadOutlined
} from '@ant-design/icons-vue';
//引入
import { copy } from 'clipboard';
import { useStore } from '@/store'
import { useResizeObserver  } from '@vueuse/core'
import { useDrag } from '../views/hooks/useDrag'
import { message, Progress, Modal } from 'ant-design-vue';
import ContextMenu from './ContextMenu.vue'
import fileService from '../api/file'
import { notification } from 'ant-design-vue';
import GlobalUploader from '@/components/GlobalUploader/GlobalUploader.vue'
import bus from '@/components/GlobalUploader/utils/bus.ts'
import { validateFolderName } from "@/utils/index"
const props = defineProps<{ levelList: string[], changeLevelIndex:Function }>();
const emit = defineEmits(['close', 'openFile']);
const store = useStore();

for(let item of store.userInfo.userClusterInfo) {
  item.usedCoreTime = store.userInfo.roleType == 2 ? Number(item.residueCoreTime) : Number(item.parentCanUsedCoreTime)
}
store.userInfo.userClusterInfo.sort((a: any, b: any) => b.usedCoreTime - a.usedCoreTime);
// id
const gidNumber = ref(store.userInfo.userClusterInfo[0].gidNumber || "");
// 当前集群分区
const clusterInfo = computed(() => {
  return store.userInfo.userClusterInfo.find((item: any) => item.gidNumber == gidNumber.value) || {};
})
// 弹窗
const showModal = ref(true);
// 全屏
const isFullModal = ref(false);
// 拖拽
const filemodalRef = ref();
const transformStyle = ref(useDrag(filemodalRef));
const columns = [
  { title: '名称', dataIndex: 'text',  key: 'text', sorter: (a: any, b: any) => a.text.toLowerCase() > b.text.toLowerCase(),},
  { width: 140, title: '类型', dataIndex: 'type', key: 'type' },
  { width: 100, title: '目录个数', dataIndex: 'numberOfDir', key: 'numberOfDir' },
  { width: 200, title: '修改时间', dataIndex: 'updateTime', key: 'updateTime', sorter: (a: any, b: any) => new Date(a.updateTime).getTime() - new Date(b.updateTime).getTime() },
  { width: 140, title: '大小', dataIndex: 'size', key: 'size', sorter: (a: any, b: any) => b.fileSize - a.fileSize},
];

const fileboxRef = ref();
const tableScrolly = ref(0);
onMounted(async () => {
  directory.value = clusterInfo.value.currentDirectory;
  store.working.push('file');
  await getFileList();
  nextTick(() => {
    useResizeObserver(fileboxRef.value, (entries) => { //监听终端窗口
      if(!fileboxRef.value) return;
      const { height } = entries[0].contentRect;
      tableScrolly.value = height - 75 - 75;
    });
  });
  /**监听文件上传成功 */
  uploadRef.value.uploader.on('fileComplete', function (_rootFile: any) {
    getFileList()
  })
});

// onUnmounted(() => {
//   uploadRef.value.uploader.off('fileComplete')
// })

/**选择集群分区 */
function changeCluster() {
  directory.value = clusterInfo.value.currentDirectory;
  getFileList()
}

const loading = ref(false);
const searchKw = ref('');
const dataList = ref<any>([]);
const directory = ref('/');
const containHiddenFile = ref(false);
const navList = ref<any>([])
/**获取文件管理 */
async function getFileList(path?: string) {
  try {
    loading.value = true;
    const { data } = await fileService.getFileList(store.authInfo.tenantCode, {
      path: path || directory.value,
      containHiddenFile: containHiddenFile.value,
    }, 
    clusterInfo.value.id, 
    clusterInfo.value.url
    );
    if (searchKw.value) {
      dataList.value = data.list.filter((item: any) => item.text.includes(searchKw.value));
    } else {
      dataList.value = data.list || [];
    };
    directory.value = data.directory;
    navList.value = data.directory.replace(clusterInfo.value.currentDirectory, '').split('/');
    selectedData.value = [];
    loading.value = false;
  } catch (error) {
    console.log(error)
    loading.value = false;
  }
}

/**打开文件夹 */
async function openFile(item: any) {
  if(item.fileType == '目录')  {
    directory.value += directory.value == '/' ?  item.text : `/${item.text}`;
    getFileList();
    return;
  };
  if (getFileExtension(item.text) != '压缩文件') {
    emit('openFile', {...item, directory: directory.value, clusterInfo: clusterInfo.value});
    props.changeLevelIndex('webEdit');
    return;
  }
  message.error('该文件不可编辑');
}

/**返回上一级 */
async function backDir() {
  if (loading.value) return;
  if (directory.value == clusterInfo.value.currentDirectory) return;
  const lastStr = navList.value.pop();
  const path = directory.value.replace(`/${lastStr}`, '')
  getFileList(path);
}

/**导航跳转 */
function navChange(index: number) {
  if (loading.value) return;
  directory.value = clusterInfo.value.currentDirectory + navList.value.slice(0, index + 1).join('/');
  getFileList()
};

/**右键菜单 */
const isShowMenu = ref(false);
const menuPosition = ref({x: 0, y: 0});
const menus = ref<Array<any>>([]);
const curItem = ref();
function showMenu(event: any, item?: any) {
  if (item) {
    curItem.value = item;
    menus.value = ['download', 'refresh', 'delete', 'newfile', 'rename', 'copyPath', 'copyFile', 'zipFile', 'pasteFile', 'moveFile'];
    selectedData.value = Array.from(new Set([...selectedData.value, item.text]));
  } else {
    curItem.value = undefined;
    menus.value = ['refresh', 'newfile', 'copyPath', 'pasteFile', 'moveFile'];
  }
  menuPosition.value.x = event.clientX;
  menuPosition.value.y = event.clientY;
  isShowMenu.value = true;
  event.stopPropagation();
}

/**右键菜单 */
function menuChange({ menuType }: any) {
  switch(menuType) {
    case 'download' : download(curItem.value); break;
    case 'refresh' : getFileList(); break;
    case 'delete' : deleteFile(curItem.value.text); break;
    case 'newfile' : showfolderModel.value = true; break;
    case 'rename' : 
    showRenameModel.value = true; 
    formRename.value.newName = curItem.value.text; 
    break;
    case 'copyPath' : copyPath(directory.value); break;
    case 'copyFile' :
    transferFile.value = Array.from(new Set([...selectedList.value, curItem.value]));
    break;
    case 'zipFile' : 
    selectedData.value = Array.from(new Set([...selectedData.value, curItem.value.text]));
    selectedList.value = Array.from(new Set([...selectedList.value, curItem.value]));
    showZipModel.value = true;
    break;
    case 'pasteFile' : showTransferModel.value = {type: 1, open: true, title: '复制' }; break;
    case 'moveFile' : showTransferModel.value = { type: 2, open: true, title: '移动' };; break;
  };
  isShowMenu.value = false;
}

/**复制路径 */
async function copyPath(text: string) {
  copy(curItem.value ? `${text}/${curItem.value?.text}` : text)
  message.success('复制成功')
}

// 复制、粘贴弹窗
const showTransferModel = ref({type: 0, title: '', open: false });
// 复制的文件
const transferFile = ref<any>([]);
// 转移加载
const transferLoading = ref(false);
/**移动或粘贴 */
async function submitTransfer() {
  try {
    transferLoading.value = true;
    const keys = transferFile.value.map((item: any) => item.text)
    if (dataList.value.some((item: any) => (keys.includes(item.text)))) {
      transferLoading.value = false;
      message.warning('当前目录已存在重复的文件！')
      return;
    };
    for(let file of transferFile.value) {
      const params = {
        fromPath: file.pwd,
        toPath: directory.value,
      };
      if (showTransferModel.value.type == 1) {
        await fileService.copyFile(store.authInfo.tenantCode, params, clusterInfo.value.id)
      } else {
        await fileService.moveFile(store.authInfo.tenantCode, params, clusterInfo.value.id)
      };
    }
    getFileList();
    transferFile.value = []
    transferLoading.value = false;
    showTransferModel.value = {type: 0, title: '', open: false }
  } catch (error) {
    transferLoading.value = false;
  }
}

const fileType = ['tar', 'rar', 'zip']
/**获取文件后缀 */
function getFileExtension(filename: string) { 
  const dotIndex = filename.lastIndexOf('.');  
  if (dotIndex === -1) return '文件';  
  const name = filename.substring(dotIndex + 1); // 使用substring()获取'.'之后的部分  
  return fileType.includes(name) ? '压缩文件' : name;  
}

const isUpdataMask = ref(false); // 拖拽蒙层
const uploadRef = ref();
/**上传文件 */
function openUpload(file?: File) {
  if (file) {
    uploadRef.value.panelShow = true;
    uploadRef.value.uploader.addFile(file)
  } else {
    bus.emit('openUploader', {params: {}, options: {}})
  }
}

/**上传目录 */
function openUploadFolder() {
  bus.emit('openUploaderFolder', {params: {}, options: {}});
}


const showfolderModel = ref(false);
const formFolder = ref({  directory: '', folderName: '' });
const createdloading = ref(false);
/**创建文件夹 */
async function submitFolder() {
  try {
    createdloading.value = true;
    formFolder.value.directory =  directory.value;
    const checkName = validateFolderName(formFolder.value.folderName);
    if (!checkName.isValid) {
      message.warning(checkName.message);
      createdloading.value = false;
      return;
    }
    if(dataList.value.some((item: any) => (formFolder.value.folderName == item.text))) {
      message.warning('当前已存在重复的文件！');
      createdloading.value = false;
      return;
    }
    await fileService.addNewFile(store.authInfo.tenantCode, formFolder.value, clusterInfo.value.id)
    message.success('创建成功');
    getFileList();
    createdloading.value = false;
    showfolderModel.value = false;
    formFolder.value.folderName = "";
  } catch (error) {
    createdloading.value = false;
  }
}

const showRenameModel = ref(false);
const formRename = ref({
  directory: '',
  oldName: '',
  newName: '',
});
const uploading = ref(false);
/**文件重命名 */
async function submitRename() {
  try {
    const checkName = validateFolderName(formRename.value.newName);
    if (!checkName.isValid) {
      message.warning(checkName.message);
      uploading.value = false;
      return;
    }
    uploading.value = true;
    formRename.value.directory =  directory.value;
    formRename.value.oldName = curItem.value.text;
    await fileService.setFileName(store.authInfo.tenantCode, formRename.value, clusterInfo.value.id);
    message.success('修改成功');
    getFileList();
    uploading.value = false;
    showRenameModel.value = false;
  } catch (error) {
    uploading.value = false;
  }
}

/**选择文件 */
type Key = string | number;
const selectedData = ref<any>([]);
const selectedList = ref<any>([])
const onSelectChange = (selectedRowKeys: Key[], list: any) => {
  selectedData.value = selectedRowKeys;
  selectedList.value = list;
};

const dowloading = ref(false);
/**批量下载文件 */
async function downloadFile() {
  dowloading.value = true;
  try {
    for(let item of dataList.value) {
      if (selectedData.value.includes(item.text)) {
        await download(item);
      }
    }
    dowloading.value = false;
    selectedData.value = [];
  } catch {
    dowloading.value = false;
  }
}

/**单个下载 */
async function download(item: any) {
  try {
    if (item.fileType == '目录'){
      dowloading.value = false;
      message.warning('暂不支持目录下载');
      return;
    }
    const filePath = `${directory.value == '/' ? '' : directory.value}/${item.text}`;
    const { data } = await fileService.downUrl(store.authInfo.tenantCode, filePath, clusterInfo.value.id, clusterInfo.value.url);
    // 桌面应用跳转浏览器下载
    if(window.electronAPI) {
      window.electronAPI.sendMessageToMain({ type: 'download', url: data  });
    } else {
      const a = document.createElement('a');
      a.href = data;
      a.target = "_blank";
      a.download = item.text;
      a.click();
    }
  } catch (error) {
    console.log(error)
  }
};

const delloading = ref(false);
/**删除文件 */
async function deleteFile(text?: string) {
  const textStr = selectedData.value.length == 1 ? `确定要删除${selectedData.value[0]}该文件吗` : `确定要删除所选择文件吗`
    Modal.confirm({
      title: '提示',
      content: textStr,
      onOk: async () => {
        try {
          delloading.value = true;
          const params = {
            directory: directory.value,
            paths: text ? [text] : selectedData.value,
            clusterId: clusterInfo.value.clusterId
          }
          const { data } = await fileService.deleteFile(store.authInfo.tenantCode, params, clusterInfo.value.id, clusterInfo.value.url);
          if (data) {
            clearUploaderFile(text ? [text] : selectedData.value)
            message.success('删除成功');
            getFileList();
          } else {
            message.error('删除失败');
          }
          delloading.value = false;
        } catch(error) {
          console.log(error)
          delloading.value = false;
        }
      },
      onCancel: () => {
        delloading.value = false;
      }
    })
};

/**
 * 删除上传任务中文件
 * @param list 
 */
function clearUploaderFile(list: string[]) {
  if (uploadRef.value.uploader) {
    uploadRef.value.uploader.fileList.forEach((item: any) => {
      if (list.includes(item.name)) {
        uploadRef.value.uploader.removeFile(item)
      }
    })
  }
}

// 压缩文件弹窗
const showZipModel = ref(false);
const formCompress = ref({ compressName: '' });
// 压缩loading
const compressing = ref(false);
// 压缩格式
const format = ref('.tar.gz');
// 压缩任务
const progressTask = ref<{[key: string]: any}>({});
// 当前压缩任务
const currentTaskId = ref('');
/*开始压缩 */
async function compressFile() {
  try {
    const fileName = `${formCompress.value.compressName}${format.value}`;
    const taskList = Object.values(progressTask.value);
    const params = {
      filePath: selectedList.value.map((item: any) => item.pwd),
      targetPath: `${directory.value}/${formCompress.value.compressName}${format.value}`,
    }
    if(dataList.value.some((item: any) => (fileName == item.text))) {
      message.warning('存在重复命名文件！');
      return;
    }
    if(taskList.length > 0 && Object.values(progressTask.value).some((item: any) => (fileName == item.fileName))) {
      message.warning('已有相同命名正在压缩');
      return;
    }
    compressing.value = true;
    const { data } = await fileService.compressZip(store.authInfo.tenantCode, params, clusterInfo.value.id);
    currentTaskId.value = data;
    progressTask.value[data] = { 
      taskId: data, 
      progress: 0, 
      fileName: fileName 
    };
    fileService.getCompressTask(store.authInfo.tenantCode, data, (progress: any) => {
      if (progress == 'done') {
        getFileList();
        compressing.value = false;
        formCompress.value.compressName = '';
        currentTaskId.value = '';
        showZipModel.value = false;
        message.success('压缩成功');
        notification.close(data);
        delete progressTask.value[data];
        return;
      }
      progressTask.value[data].progress = parseInt(progress)
    }, clusterInfo.value.id);
  } catch(error) {
    console.log(error)
    compressing.value = false;
  }
};

/**关闭弹窗打开压缩进度 */
function closeZipModel(taskInfo: any) {
  compressing.value = false;
  formCompress.value.compressName = '';
  currentTaskId.value = '';
  if (taskInfo && compressing.value) {
    notification.open({
      message: `正在压缩 ${taskInfo.fileName}`,
      description: () => h ( Progress, { percent: taskInfo.progress || 0 }),
      placement: 'bottomRight',
      duration: null,
      key: taskInfo.taskId,
      onClose: () => {}
    })
  }
}

const errorTips = ref<{[key: string]: boolean}>({})
/**核时监听-是否欠费 */
watch(() => store.refreshUserTime, () => {
  // 存在正在上传的文件(不提醒)
  if (uploadRef.value.uploader.isUploading()) return;
  // 主账户/子账户剩余核时
  const usedCoreTime = store.userInfo.roleType == 2 ? Number(clusterInfo.value.residueCoreTime) : Number(clusterInfo.value.parentCanUsedCoreTime)
  // 是否欠费
  if (usedCoreTime <= 0 && !errorTips.value[clusterInfo.value.id]) {
    Modal.error({
      class: 'remind-modal',
      title: '核时欠费',
      content: '您当前集群分区余额核时不足，将关闭当前文件管理，请及时联系管理员充值',
      onOk: () => {
        close();
        delete errorTips.value[clusterInfo.value.id];
      },
      getContainer: () => fileboxRef.value
    });
    errorTips.value[clusterInfo.value.id] = true
  }
})

/**打开文件管理 */
function open() {
  showModal.value = true;
}

/**隐藏弹窗 */
function hide() {
  showModal.value = false;
}

/**关闭弹窗 */
function close() {
  if (uploadRef.value.uploader.isUploading()) {
    return message.warning('存在正在上传的文件')
  }
  uploadRef.value.uploader.cancel();
  showModal.value = false;
  store.working.splice(store.working.indexOf('file'), 1);
  emit('close');
};

defineExpose({open, close})
</script>

<style scoped lang="scss" >
@import url(./antdmodal.scss);
.container {
  width: 100%;
  margin-top: 12px;
  height: 100%;
  position: relative;
  .mask-updata {
    height: 90%;
    width: 100%;
    position: absolute;
    top: 60px;
    left: 0;
    background: #666666;
    opacity: 0.5;
    z-index: 2;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
    :deep(.ant-upload-wrapper) {
      width: 100%;
      height: 100%
    }
    .mask-tips {
      pointer-events: none;
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      z-index: 0;
      color: #fff;
      .up-icon {
        margin-right: 12px;
        font-size: 20px;
      }
    }

  }
  .card-head {
    padding: 0 20px;
  }

  :deep(.ant-page-header) {
    padding: 8px 24px;
  }
  .nav-list {
    padding: 10px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .nav-left {
      cursor: pointer;
      font-size: 15px;
      display: flex;
      align-items: center;
      &:hover {
        color: #74bfff;
      }
      .nav-h {
        font-size: 15px;
        color: #469df0;
      }
    }
    .nav-name {
      cursor: pointer;
    }
  }
  .card {
    padding: 20px;
    padding-top: 0;
    padding-bottom: 0;
    :deep(.ant-table-tbody>tr>td) {
      padding-top: 4px;
      padding-bottom: 4px;
    }
    .file-name {
      cursor: pointer;
      margin-left: 10px;
    }
  }

  .card-bottom {
    padding: 0 20px;
    width: 100%;
    height: 42px;
    position: absolute;
    bottom: 0;
    left: 0;
  }
}

:deep(.ant-upload.ant-upload-btn) {
  padding: 80px 0;
}
:deep(.ant-upload-text) {
  color: #666666!important;
}

.transfer {
  color: #666666;
  padding: 16px;
  .line>span:nth-child(1) {
    font-size: 16px;
  }
  .transfer-tips {
    font-size: 12px;
    margin-top: 16px;
    color: #74bfff;
  }
}
// 核时提醒
:deep(.remind-modal .ant-modal-content){
  padding: 20px 24px!important;
  font-weight: 500;
}
:deep(.remind-modal .ant-modal-header){
  border-bottom: none;
}
</style>
