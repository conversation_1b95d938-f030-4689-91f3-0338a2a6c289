import { useStore } from '@/store';

export default class WebSocketClient {
  public ws!: WebSocket | null;
  private url: string;
  private connectid: string;
  private reconnectAttempts: number = 0;
  private maxReconnectAttempts: number = 1;
  private reconnectInterval: number = 1000; // 1秒  
  private heartbeatInterval: number = 10000; // 10秒  
  private heartbeatTimeout: any;
  // 连接回调
  public openCallback: Function | undefined;
  public messageCallback: Function | undefined;
  // 重新连接回调
  public reconnectCallback: Function | undefined;
  constructor(connectid: string) {
    this.connectid = connectid;
    this.url = this.getWebSocketUrl();
    this.initWebSocket();
    // 启动心跳检测  
    this.startHeartbeat();
  }

  /**
   * 根据location路径生成WebSocket地址
   * @returns {string} WebSocket地址
   */
  private getWebSocketUrl (): string {
    let protocol = 'ws://';
    if (window.location.protocol === 'https:') {
      protocol = 'wss://';
    }
    return protocol + import.meta.env.VITE_APP_WS_URL + `/shell?token=${useStore().authInfo.token}&connectid=${this.connectid}`;
  }

  private initWebSocket(): void {
    // 1. 彻底关闭旧连接
    if (this.ws) {
      this.stopHeartbeat(); // 停止心跳
      this.ws.onopen = null;
      this.ws.onmessage = null;
      this.ws.onerror = null;
      this.ws.onclose = null;
      this.ws.close();
      this.ws = null; // 确保旧连接被垃圾回收
    }

    this.ws = new WebSocket(this.url);
    this.ws.onopen = () => {
      this.reconnectAttempts = 0; // 重置重连尝试次数 
      // console.log('WebSocket is open now.', event);
      if (this.openCallback) this.openCallback();
    };
    this.ws.onmessage = (event) => {
      // console.log('WebSocket message received:', event.data);
      // 重置心跳超时  
      this.resetHeartbeatTimeout();
      if (this.messageCallback) this.messageCallback(event.data);
    };

    this.ws.onerror = (event: any) => {
      console.error('WebSocket error observed:', event);
      // 关闭并尝试重连  
      this.closeAndReconnect();
    };

    this.ws.onclose = (event: any) => {
      if (event.wasClean) {
        console.log('WebSocket closed cleanly, code:', event.code, 'reason:', event.reason);
      } else {
        console.error('WebSocket died unexpectedly: code:', event.code, 'reason:', event.reason);
        // 尝试重连  
        this.closeAndReconnect();
      }
    };
  }

  private closeAndReconnect(): void {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.url = this.getWebSocketUrl();
      setTimeout(() => {
        console.log('WebSocket reconnecting...');
        this.initWebSocket();
        this.reconnectAttempts++;
        if (this.reconnectCallback) this.reconnectCallback();
      }, this.reconnectInterval);
    } else {
      console.error('Max reconnect attempts exceeded.');
      // 可能需要执行一些额外的操作，如通知用户  
    }
  }

  public send(message: string): void {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.ws.send(message);
    } else {
      console.error('WebSocket is not open, unable to send message.');
    }
  }

  private startHeartbeat(): void {
    this.heartbeatTimeout = setInterval(() => {
      if (this.ws && this.ws.readyState === WebSocket.OPEN) {
        // 发送心跳消息（如果需要）  
        this.sendHeartbeat();
      } else {
        // 如果WebSocket没有打开，停止心跳检测  
        this.stopHeartbeat();
      }
    }, this.heartbeatInterval);
  }

  private sendHeartbeat(): void {
    // 发送自定义的心跳消息，比如 'ping'  
    this.send(JSON.stringify({"operate": "command", "command": 'xtermPing'}));
  }

  private resetHeartbeatTimeout(): void {
    // 重置心跳超时  
    clearInterval(this.heartbeatTimeout);
    this.startHeartbeat();
  }

  private stopHeartbeat(): void {
    // 停止心跳检测  
    clearInterval(this.heartbeatTimeout);
  }

  // 添加关闭WebSocket的方法（如果需要）  
  public close(): void {
    if (this.ws) {
      this.ws.close();
      this.stopHeartbeat();
    }
  }
}
