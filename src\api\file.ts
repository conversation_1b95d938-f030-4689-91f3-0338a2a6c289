import { request, createSSeRequest } from '@/utils/request.ts'
export default {
  /**
	 * 文件列表
	 * @param {object} params
	 */
	getFileList(tenant: string, data: {
		path: string,
		containHiddenFile: boolean,
		// clusterId: string
	}, 
	connectid: string | number,
	connect_url: string,
  ) {
	  return request({ url: `/file-server/v1.2/${tenant}/tunnel`, 	method: 'post',	data, header: { connectid }, connect_url });
	},

	/**
	 * 下载分片预请求
	 * @param {object} data
	 * @returns
	 */
	preDownFile(tenant: string, data: {
		filePath: string
	},
	connectid: string | number,
	connect_url: string,) {
	  return request({ url: `/file-server/v2.0/${tenant}/pre-down`, 	method: 'post', data, header: { connectid }, connect_url });
	},

	/**
	 * 文件下载
	 * @param {object} data
	 * @returns
	 */
	downFile(tenant: string, data: {
		filePath: string,
		part: number
	}, 
	connectid: string | number,
	connect_url: string,) {
	  return request({ url: `/file-server/v2.0/${tenant}/down`, 	method: 'post', data, responseType: 'blob', header: { connectid }, connect_url });
	},

	/**
	 * 获取文件下载url
	 * @param {object} data
	 * @returns
	 */
	downUrl(tenant: string, path: string, 	
		connectid: string | number,
		connect_url: string,) {
	  return request({ url: `/file-server/v1.0/${tenant}/download-url`, 	method: 'post', data: { path }, header: { connectid }, connect_url });
	},

  /**
	 * 文件上传
	 * @param {object} data
	 */
	uploadFile(tenant: string, data: FormData, 		
		connectid: string | number,
		connect_url: string,) {
	  return request({ url: `/file-server/v1.0/${tenant}/on`, 	method: 'post',	data, header: { connectid }, connect_url });
	},

  /**
	 * 删除文件
	 * @param {object} params
	 */
	deleteFile(tenant: string, params: {
    directory: string,
    paths: string[],
		clusterId: string
  }, 		
	connectid: string | number,
	connect_url: string,) {
	  return request({ url: `/file-server/v1.0/${tenant}/tunnel`, 	method: 'delete',	data: params, header: { connectid }, connect_url });
	},

	/**
	 * 新建文件夹
	 * @param {object} data
	 */
	addNewFile(tenant: string, data: {
    directory: string,
    folderName: string
  }, 
	connectid: string | number) {
	  return request({ url: `/file/v1.0/${tenant}/new-folder`, 	method: 'post',	data, header: { connectid } });
	},

	/**
	 * 文件重命名
	 * @param {object} data
	 */
	setFileName(tenant: string, data: {
    directory: string,
		oldName: string,
    newName: string
  }, 
	connectid: string | number) {
	  return request({ url: `/file/v1.0/${tenant}/rename`, 	method: 'post',	data, header: { connectid } });
	},

	/**
	 * 复制文件
	 * @param {object} data
	 */
	copyFile(tenant: string, data: {
    fromPath: string,
		toPath: string,
  }, 
	connectid: string | number) {
	  return request({ url: `/file/v1.0/${tenant}/copy`, 	method: 'post',	data, header: { connectid }  });
	},

	/**
	 * 移动文件
	 * @param {object} data
	 */
	moveFile(tenant: string, data: {
    fromPath: string,
		toPath: string,
  },
	connectid: string | number) {
	  return request({ url: `/file/v1.0/${tenant}/move`, 	method: 'post',	data, header: { connectid } });
	},

	/**
	 * 查看文件格式
	 * @param {object} data
	 */
	getfileEncoding(tenant: string, data: {
    filePath: string,
  }, 
	connectid: string | number
	) {
	  return request({ url: `/file/v1.0/${tenant}/fileEncoding`, 	method: 'post',	data, header: { connectid } });
	},

	/**
	 * 压缩文件
	 * @param {object} data
	 */
	compressZip(tenant: string, data: {
		filePath: Array<string>,
		targetPath: string,
	},
	connectid: string | number
) {
		return request({ url: `/file/v1.0/${tenant}/compress`, 	method: 'post',	data, header: { connectid } });
	},

	/**
	 * 获取压缩任务进度
	 * @param {object} data
	 */
	getCompressTask(tenant: string, id: string, callBackFn: Function, connectid: string | number) {
		createSSeRequest({
			url: `/file/v1.0/${tenant}/compress-schedule?compressId=${id}`,
			header: { connectid },
			onProgress: callBackFn
		})
	}
}
