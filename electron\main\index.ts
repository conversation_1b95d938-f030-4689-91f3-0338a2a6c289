import { app, BrowserWindow, shell, ipcMain, Menu, MenuItem, screen, globalShortcut, Tray } from 'electron'
import { createRequire } from 'node:module'
import { fileURLToPath } from 'node:url'
import path from 'node:path'
import os from 'node:os'

const require = createRequire(import.meta.url)
const __dirname = path.dirname(fileURLToPath(import.meta.url))

// The built directory structure
//
// ├─┬ dist-electron
// │ ├─┬ main
// │ │ └── index.js    > Electron-Main
// │ └─┬ preload
// │   └── index.mjs   > Preload-Scripts
// ├─┬ dist
// │ └── index.html    > Electron-Renderer
//
process.env.APP_ROOT = path.join(__dirname, '../..')

export const MAIN_DIST = path.join(process.env.APP_ROOT, 'dist-electron')
export const RENDERER_DIST = path.join(process.env.APP_ROOT, 'dist')
export const VITE_DEV_SERVER_URL = process.env.VITE_DEV_SERVER_URL

process.env.VITE_PUBLIC = VITE_DEV_SERVER_URL
  ? path.join(process.env.APP_ROOT, 'public')
  : RENDERER_DIST

// Disable GPU Acceleration for Windows 7
if (os.release().startsWith('6.1')) app.disableHardwareAcceleration()

// Set application name for Windows 10+ notifications
if (process.platform === 'win32') app.setAppUserModelId(app.getName())

if (!app.requestSingleInstanceLock()) {
  app.quit()
  process.exit(0)
}

// 快捷键
const preShortCut = {
  openApp: 'Ctrl+E',
  openDevTools: 'Ctrl+Shift+L', 
};

let win: BrowserWindow | null = null
const preload = path.join(__dirname, '../preload/index.mjs')
const indexHtml = path.join(RENDERER_DIST, 'index.html')
const indexUrl = 'https://cloud.phadcloud.com'
// const indexUrl = 'https://mscloud.phadcloud.com:8443'
// const indexUrl = 'https://tjs.phadcloud.com'
async function createWindow() {
  const size = screen.getPrimaryDisplay().workAreaSize;
  const minWidth = 1000;
  const winWidth = minWidth > 800 ? minWidth : 800;
  const winHeight = Math.ceil(size.height  * 0.6);
  win = new BrowserWindow({
    width: winWidth,
    height: winHeight,
    minWidth: 800,
    minHeight: 632,
    title: '天玑智算云',
    icon: path.join(process.env.VITE_PUBLIC, 'favicon.png'),
    webPreferences: {
      preload
      // Warning: Enable nodeIntegration and disable contextIsolation is not secure in production
      // nodeIntegration: true,

      // Consider using contextBridge.exposeInMainWorld
      // Read more on https://www.electronjs.org/docs/latest/tutorial/context-isolation
      // contextIsolation: false,
    },
  });

  Menu.setApplicationMenu(null);
  if (VITE_DEV_SERVER_URL) { // #298
    win.loadURL(VITE_DEV_SERVER_URL)
    // win.loadURL(indexUrl)
    // Open devTool if the app is not packaged
    win.webContents.openDevTools()
  } else {
    win.loadURL(indexUrl)
    // win.loadFile(indexHtml)
  }

  // 系统托盘右键菜单
  const trayMenuTemplate = [{
    label: '退出',
    click: function () { app.quit() }
  }];
  // 托盘对象
  let appTray: any = null;
  if ( process.platform === 'darwin') {
    // console.log('这是mac系统');
    // appTray = new Tray(path.join(trayIcon, 'favicon.24x24.png'));
  } else {
    appTray = new Tray(path.join(process.env.VITE_PUBLIC, 'favicon.png'));
  }

  // 设置此图标的上下文菜单
  appTray.setContextMenu(Menu.buildFromTemplate(trayMenuTemplate));


  // 正式版隐性 打开调试窗口快捷键
  globalShortcut.register(preShortCut.openDevTools, () => {
    win?.webContents.openDevTools();
  });

  // Test actively push message to the Electron-Renderer
  win.webContents.on('did-finish-load', () => {
    win?.webContents.send('main-process-message', new Date().toLocaleString());
  })

  // Make all links open with the browser, not with the application
  win.webContents.setWindowOpenHandler(({ url }) => {
    if (url.startsWith('https:')) shell.openExternal(url)
    return { action: 'deny' }
  })
  // win.webContents.on('will-navigate', (event, url) => { }) #344
}

app.whenReady().then(createWindow)

app.on('window-all-closed', () => {
  win = null
  if (process.platform !== 'darwin') app.quit()
})

app.on('second-instance', () => {
  if (win) {
    // Focus on the main window if the user tried to open another
    if (win.isMinimized()) win.restore()
    win.focus()
  }
})

app.on('activate', () => {
  const allWindows = BrowserWindow.getAllWindows()
  if (allWindows.length) {
    allWindows[0].focus()
  } else {
    createWindow()
  }
})

// 创建上下文菜单
const createContextMenu = (params: any) => {
  const menu = new Menu()
  // 标准编辑功能
  menu.append(new MenuItem({ label: '剪切  Ctrl+X', role: 'cut', enabled: params.editFlags.canCut }));
  menu.append(new MenuItem({ label: '复制  Ctrl+C', role: 'copy', enabled: params.editFlags.canCopy }));
  menu.append(new MenuItem({ label: '粘贴  Ctrl+Shift+V', role: 'paste', enabled: params.editFlags.canPaste }))
  menu.append(new MenuItem({ label: '全选  Ctrl+A', role: 'selectAll' }))
  // 只有选中文本时才显示这些选项
  if (params.selectionText) {
    menu.append(new MenuItem({ type: 'separator' }))
    menu.append(new MenuItem({ 
      label: params.selectionText ? '百度搜索 "' + params.selectionText + '"' : '百度搜索',
      click: () => {
        require('electron').shell.openExternal(
          `https://www.baidu.com/s?q=${encodeURIComponent(params.selectionText)}`
        )
      }
    }))
  }
  menu.popup({ window: win as BrowserWindow  })
}

ipcMain.on('message-from-renderer', (_, params) => {
  // 跳转浏览器下载
  if (params.type == 'download') {
    shell.openExternal(params.url);
    return;
  }
  createContextMenu(params)
})

// New window example arg: new windows url
ipcMain.handle('open-win', (_, arg) => {
  const childWindow = new BrowserWindow({
    webPreferences: {
      preload,
      nodeIntegration: true,
      contextIsolation: false,
    },
  })

  if (VITE_DEV_SERVER_URL) {
    childWindow.loadURL(`${VITE_DEV_SERVER_URL}#${arg}`)
  } else {
    childWindow.loadFile(indexHtml, { hash: arg })
  }
})
