<template>
  <a-config-provider :locale="zhCN">
    <a-spin :spinning="spinning" style="top: 50%; transform: translateY(-50%);" :tip="`正在登入${store.baseInfo?.portalTitle || '天玑智算云'}☁️...`">
      <div class="app-container">
        <router-view v-show="!spinning" />
      </div>
    </a-spin>
  </a-config-provider>
</template>

<script setup lang="ts">
import loginService  from '@/api/login'
import zhCN from 'ant-design-vue/es/locale/zh_CN';
import { onMounted, ref } from 'vue';
import { useStore } from '@/store';
import * as dayjs from 'dayjs';
import "dayjs/locale/zh-cn";
dayjs.locale("zh-cn");
const store = useStore();
const spinning = ref(true);
onMounted(async () => {
  try {
    await loginService.getBaseInfo(location.host);
    spinning.value = false;
  } catch (error) {
    console.log(error)
  }
})
</script>

<style scoped>
.app-container {
  min-height: 100vh;
  min-width: 100vw;
  background-color: #96d6fe;
}
</style>

