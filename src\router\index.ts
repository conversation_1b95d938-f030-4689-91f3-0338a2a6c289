import { createRouter, createWebHashHistory } from 'vue-router'
import {useStore} from '@/store'
import loginService  from '@/api/login'
//系统路由
const routes = [
  {
    name: 'layout',
    path: '/',
    component: () => import('@/layout/index.vue'),
    redirect: 'home',
    children: [
			{
				name: 'home',
				path: '/home',
				meta: { title: '首页', icon: 'icon-home', hidden: false, type: 'M' },
				component: () => import('@/views/home.vue'),
			},
		]
  }, 
  {
    name: 'login',
    path: '/login',
    component: () => import('@/views/login.vue'),
    meta: { title: '登录' }
  },
  {
    path: "/:pathMatch(.*)*",
    hidden: true,
    meta: { title: '访问的页面不存在' },
    component: () => import('@/layout/404.vue'),
  }
]
const router = createRouter({
	history: createWebHashHistory(),
	routes,
})

const whiteRoute = ['login']
router.beforeEach(async (to: any, _from, next) => {
	const store = useStore();
	// 未登录白名单
	if (!store.authInfo.token) {
		if(whiteRoute.includes(to.name)) return next();
		return next("/login");
	};
	// 如果已经登录，并准备进入 Login 页面，则重定向到主页
	if (to.path === "/login") {
		return next({ path: "/" });
	}
  // 获取用户信息
	try {
		if (!store.userInfo) {
			await loginService.getUserInfo(store.authInfo.tenantCode);
		}
		next();
	} catch (error) {
		store.authInfo.token = '';
    next("/login");
	}
})

export default router