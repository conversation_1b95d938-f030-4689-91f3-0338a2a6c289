<template>
  <a-modal
    :open="true"
    @cancel="emit('close')"
    :footer="null"
    width="480px"
    class="register-modal"
    :maskClosable="false"
    :closable="true"
  >
    <div class="register-container">
      <div class="register-header">
        <div class="logo-area">
          <!-- <img src="@/assets/logo.png" alt="logo" class="logo" /> -->
        </div>
        <h2>欢迎加入天玑智算</h2>
        <p>开启您的智能计算之旅</p>
      </div>

      <a-form
        :model="formState"
        name="registerForm"
        @finish="onFinish"
        layout="vertical"
      >
        <div class="form-content">
          <a-form-item
            name="name"
            :rules="[{ required: true, message: '请输入姓名' }]"
          >
            <a-input 
              v-model:value="formState.name" 
              placeholder="请输入姓名"
              size="large"
            >
              <template #prefix>
                <UserOutlined class="site-form-item-icon" />
              </template>
            </a-input>
          </a-form-item>

          <a-form-item
            name="email"
            :rules="[
              { required: true, message: '请输入邮箱' },
              { type: 'email', message: '请输入有效的邮箱地址' }
            ]"
          >
            <a-input 
              v-model:value="formState.email" 
              placeholder="请输入邮箱"
              size="large"
            >
              <template #prefix>
                <MailOutlined class="site-form-item-icon" />
              </template>
            </a-input>
          </a-form-item>

          <a-form-item
            name="phone"
            :rules="[
              { required: true, message: '请输入手机号' },
              { pattern: /^1[3-9]\d{9}$/, message: '请输入有效的手机号' }
            ]"
          >
            <a-input 
              v-model:value="formState.phone" 
              placeholder="请输入手机号"
              size="large"
            >
              <template #prefix>
                <PhoneOutlined class="site-form-item-icon" />
              </template>
            </a-input>
          </a-form-item>

          <a-form-item
            name="password"
            :rules="[
              { required: true, message: '请输入密码' },
              { min: 6, message: '密码长度不能小于6位' }
            ]"
          >
            <a-input-password 
              v-model:value="formState.password" 
              placeholder="请输入密码"
              size="large"
            >
              <template #prefix>
                <LockOutlined class="site-form-item-icon" />
              </template>
            </a-input-password>
          </a-form-item>

          <a-form-item
            name="confirmPassword"
            :rules="[
              { required: true, message: '请确认密码' },
              { validator: validateConfirmPassword }
            ]"
          >
            <a-input-password 
              v-model:value="formState.confirmPassword" 
              placeholder="请再次输入密码"
              size="large"
            >
              <template #prefix>
                <SafetyOutlined class="site-form-item-icon" />
              </template>
            </a-input-password>
          </a-form-item>

          <a-form-item
            name="organization"
            :rules="[{ required: true, message: '请输入单位名称' }]"
          >
            <a-input 
              v-model:value="formState.organization" 
              placeholder="请输入单位名称"
              size="large"
            >
              <template #prefix>
                <BankOutlined class="site-form-item-icon" />
              </template>
            </a-input>
          </a-form-item>

          <a-form-item name="inviteCode">
            <a-input 
              v-model:value="formState.inviteCode" 
              placeholder="请输入邀请码（选填）"
              size="large"
            >
              <template #prefix>
                <GiftOutlined class="site-form-item-icon" />
              </template>
            </a-input>
          </a-form-item>

          <a-form-item name="verifyCode">
            <a-row :gutter="16">
              <a-col :span="15">
                <a-input 
                  v-model:value="formState.verifyCode" 
                  placeholder="请输入验证码"
                  size="large"
                >
                  <template #prefix>
                    <SafetyCertificateOutlined class="site-form-item-icon" />
                  </template>
                </a-input>
              </a-col>
              <a-col :span="9">
                <a-button 
                  :disabled="countdown > 0"
                  @click="sendVerifyCode"
                  class="verify-code-btn"
                  size="large"
                  block
                >
                  {{ countdown > 0 ? `${countdown}s` : '获取验证码' }}
                </a-button>
              </a-col>
            </a-row>
          </a-form-item>

          <div class="agreement-section">
            <a-checkbox
              v-model:checked="formState.agreement"
              :class="{ 'agreement-error': !formState.agreement }"
            >
              我已阅读并同意
              <a @click.prevent="showAgreement">《用户协议》</a>
              和
              <a @click.prevent="showPrivacy">《隐私政策》</a>
            </a-checkbox>
          </div>

          <a-button 
            type="primary" 
            html-type="submit" 
            :loading="loading" 
            block 
            class="submit-btn"
            size="large"
          >
            立即注册
          </a-button>
        </div>
      </a-form>
    </div>

    <UserAgreement
      v-model:visible="agreementVisible"
      type="agreement"
    />
    <UserAgreement
      v-model:visible="privacyVisible"
      type="privacy"
    />
  </a-modal>
</template>

<script setup lang="ts">
import { reactive, ref } from 'vue';
import type { Rule } from 'ant-design-vue/es/form';
import {
  UserOutlined,
  MailOutlined,
  PhoneOutlined,
  LockOutlined,
  SafetyOutlined,
  BankOutlined,
  GiftOutlined,
  SafetyCertificateOutlined
} from '@ant-design/icons-vue';
import UserAgreement from './UserAgreement.vue';

const emit = defineEmits(['close']);
const loading = ref(false);
const countdown = ref(0);
const agreementVisible = ref(false);
const privacyVisible = ref(false);

const formState = reactive({
  name: '',
  email: '',
  phone: '',
  password: '',
  confirmPassword: '',
  organization: '',
  inviteCode: '',
  verifyCode: '',
  agreement: false
});

const validateConfirmPassword = async (_rule: Rule, value: string) => {
  if (!value) {
    return Promise.reject('请确认密码');
  }
  if (value !== formState.password) {
    return Promise.reject('两次输入的密码不一致');
  }
  return Promise.resolve();
};

const sendVerifyCode = async () => {
  countdown.value = 60;
  const timer = setInterval(() => {
    countdown.value--;
    if (countdown.value <= 0) {
      clearInterval(timer);
    }
  }, 1000);
};

const showAgreement = () => {
  agreementVisible.value = true;
};

const showPrivacy = () => {
  privacyVisible.value = true;
};

const onFinish = async (values: any) => {
  if (!formState.agreement) {
    return;
  }
  
  loading.value = true;
  try {
    console.log('注册信息:', values);
    emit('close');
  } catch (error) {
    console.error('注册失败:', error);
  } finally {
    loading.value = false;
  }
};
</script>

<style scoped lang="scss">
.register-modal {
  :deep(.ant-modal-content) {
    padding: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.85));
    backdrop-filter: blur(10px);
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.3);
    overflow: hidden;
  }

  :deep(.ant-modal-close) {
    top: 16px;
    right: 16px;
    color: #666;
  }
}

.register-container {
  padding: 24px 40px 32px;
}

.register-header {
  text-align: center;
  margin-bottom: 32px;

  .logo-area {
    margin-bottom: 16px;
    
    .logo {
      width: 64px;
      height: 64px;
    }
  }

  h2 {
    font-size: 24px;
    color: #1a1a1a;
    margin-bottom: 8px;
    font-weight: 600;
  }

  p {
    color: #666;
    font-size: 14px;
    margin: 0;
  }
}

.form-content {
  :deep(.ant-form-item) {
    margin-bottom: 20px;

    .ant-input-affix-wrapper {
      border-radius: 8px;
      border: 1px solid #e8e8e8;
      transition: all 0.3s;
      background: rgba(255, 255, 255, 0.8);

      &:hover, &:focus {
        border-color: #ee1e52d3;
        box-shadow: 0 0 0 2px rgba(238, 30, 82, 0.1);
      }

      .site-form-item-icon {
        color: #bfbfbf;
      }

      input {
        background: transparent;
      }
    }
  }
}

.verify-code-btn {
  height: 40px;
  border-radius: 8px;
  background: #f5f5f5;
  border: 1px solid #e8e8e8;
  color: #666;
  transition: all 0.3s;
  padding: 0;

  &:not(:disabled):hover {
    background: #ee1e52d3;
    border-color: #ee1e52d3;
    color: #fff;
  }

  &:disabled {
    background: #f5f5f5;
    color: #999;
  }
}

.agreement-section {
  margin-bottom: 24px;
  text-align: center;

  :deep(.ant-checkbox-wrapper) {
    color: #666;

    &.agreement-error {
      .ant-checkbox-inner {
        border-color: #ff4d4f;
      }
    }

    a {
      color: #ee1e52d3;
      text-decoration: none;
      transition: color 0.3s;

      &:hover {
        color: #d41a49;
      }
    }
  }
}

.submit-btn {
  height: 44px;
  border-radius: 8px;
  background: linear-gradient(135deg, #ee1e52d3, #d41a49);
  border: none;
  font-size: 16px;
  font-weight: 500;
  transition: all 0.3s;

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(238, 30, 82, 0.2);
    background: linear-gradient(135deg, #d41a49, #ee1e52d3);
  }

  &:active {
    transform: translateY(0);
  }
}
</style>
